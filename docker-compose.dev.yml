# Docker Compose配置文件 - Discord集成开发环境
# 专门用于Discord集成功能的开发和测试
# 基于docker-compose.test.yml，针对Discord功能优化

services:
  # PostgreSQL数据库服务
  postgres-dev:
    image: postgres:15-alpine
    container_name: crypto_trader_postgres_dev
    environment:
      # 数据库配置
      POSTGRES_DB: crypto_trader_dev
      POSTGRES_USER: crypto_trader
      POSTGRES_PASSWORD: dev_password_123
      # PostgreSQL配置
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"  # 使用不同端口避免冲突
    volumes:
      # 数据持久化
      - postgres_dev_data:/var/lib/postgresql/data
      # 初始化脚本
      - ./scripts/db/init_dev.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U crypto_trader -d crypto_trader_dev"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - crypto_trader_dev

  # 后端API服务
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: crypto_trader_backend_dev
    env_file:
      - .env.development
    environment:
      # 数据库连接配置
      DATABASE_URL: postgresql+asyncpg://crypto_trader:dev_password_123@postgres-dev:5432/crypto_trader_dev
      # 开发环境配置
      ENVIRONMENT: "development"
      DEBUG: "true"
      LOG_LEVEL: "DEBUG"
      TESTING: "false"
      SIMULATION_MODE: "true"
      # API配置
      API_HOST: "0.0.0.0"
      API_PORT: "8000"
      API_URL: "http://localhost:8000"
      # CORS配置
      CORS_ORIGINS: "http://localhost:5173,http://localhost:3000"
      FRONTEND_URL: "http://localhost:5173"
    ports:
      - "8000:8000"
    volumes:
      # 代码热加载 - 挂载源代码目录
      - ./backend:/app:cached
      # 日志文件挂载 - 直接挂载到宿主机目录便于开发调试
      - ./logs:/app/logs
      # 排除Python缓存和虚拟环境
      - /app/__pycache__
      - /app/.pytest_cache
    depends_on:
      postgres-dev:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/system/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - crypto_trader_dev

  # 前端Web服务
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development  # 使用开发阶段
    container_name: crypto_trader_frontend_dev
    environment:
      # Docker环境标识
      DOCKER_ENV: "true"
      # Vite开发服务器配置
      VITE_HOST: "0.0.0.0"
      VITE_PORT: "5173"
      # API配置 - 使用宿主机地址，因为前端在浏览器中运行
      VITE_API_BASE_URL: "http://localhost:8000"
      API_BASE_URL: "http://localhost:8000"
      VITE_WS_URL: "ws://localhost:8000"
      # 前端应用配置
      VITE_APP_TITLE: "AI Crypto Trading Agent - Discord开发环境"
      VITE_APP_VERSION: "1.0.0"
      VITE_DEBUG: "true"
      # 开发模式配置
      NODE_ENV: "development"
      # 热加载配置 - 关键配置项
      CHOKIDAR_USEPOLLING: "true"
      CHOKIDAR_INTERVAL: "1000"
      # Vite HMR配置 - 完整的HMR配置
      VITE_HMR_PORT: "24678"  # 使用独立的HMR端口
      VITE_HMR_HOST: "0.0.0.0"  # HMR主机地址 - Docker环境需要0.0.0.0
      VITE_HMR_PROTOCOL: "ws"  # HMR协议
      # 文件监听配置
      VITE_USE_POLLING: "true"
      VITE_POLLING_INTERVAL: "1000"
    ports:
      - "5173:5173"  # Vite开发服务器端口
      - "24678:24678"  # HMR WebSocket端口
    volumes:
      # 代码热加载 - 挂载源代码目录
      - ./frontend:/app:cached
      # 排除node_modules（使用容器内的）
      - /app/node_modules
      # 排除其他缓存目录
      - /app/.vite
      - /app/dist
    depends_on:
      backend-dev:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://0.0.0.0:5173"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - crypto_trader_dev

# 网络配置
networks:
  crypto_trader_dev:
    driver: bridge
    name: crypto_trader_dev_network

# 数据卷配置
volumes:
  postgres_dev_data:
    name: crypto_trader_postgres_dev_data
    driver: local
